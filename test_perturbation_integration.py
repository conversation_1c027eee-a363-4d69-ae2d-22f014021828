#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试摄动法集成的简单脚本
用于验证train_0211.py中的摄动法集成是否正确工作
"""

import torch
import torch.nn as nn
from model import resnet34
from perturbation_optimizer import PerturbationOptimizer
import numpy as np

def test_perturbation_integration():
    """测试摄动法集成"""
    print("=== 测试摄动法集成 ===")
    
    # 创建设备
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建简单的测试模型
    model = resnet34(num_classes=5)
    model.to(device)
    
    # 创建一些虚拟数据进行测试
    batch_size = 8
    test_data = torch.randn(batch_size, 3, 224, 224).to(device)
    test_labels = torch.randint(0, 5, (batch_size,)).to(device)
    
    print(f"测试数据形状: {test_data.shape}")
    print(f"测试标签形状: {test_labels.shape}")
    
    # 测试模型前向传播
    model.eval()
    with torch.no_grad():
        outputs = model(test_data)
        print(f"模型输出形状: {outputs.shape}")
    
    # 测试摄动优化器创建
    try:
        perturbation_optimizer = PerturbationOptimizer(
            model=model,
            device=device,
            perturbation_strength=0.01
        )
        print("✅ 摄动优化器创建成功")
    except Exception as e:
        print(f"❌ 摄动优化器创建失败: {e}")
        return False
    
    # 创建简单的数据加载器模拟
    class SimpleDataset:
        def __init__(self, data, labels):
            self.data = data
            self.labels = labels
        
        def __iter__(self):
            for i in range(len(self.data)):
                yield self.data[i:i+1], self.labels[i:i+1]
    
    simple_loader = SimpleDataset(test_data, test_labels)
    
    # 测试误差计算
    try:
        criterion = nn.CrossEntropyLoss()
        errors, samples, labels, predictions = perturbation_optimizer.calculate_sample_errors(
            simple_loader, criterion
        )
        print(f"✅ 误差计算成功，计算了 {len(errors)} 个样本的误差")
        print(f"平均误差: {np.mean(errors):.4f}")
    except Exception as e:
        print(f"❌ 误差计算失败: {e}")
        return False
    
    print("✅ 摄动法集成测试通过！")
    return True

def test_model_compatibility():
    """测试模型兼容性"""
    print("\n=== 测试模型兼容性 ===")
    
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    # 测试ResNet34模型
    try:
        model = resnet34(num_classes=5)
        model.to(device)
        
        # 测试输入
        test_input = torch.randn(1, 3, 224, 224).to(device)
        
        model.eval()
        with torch.no_grad():
            output = model(test_input)
            
        print(f"✅ ResNet34模型测试通过")
        print(f"输入形状: {test_input.shape}")
        print(f"输出形状: {output.shape}")
        print(f"输出类别数: {output.shape[1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ ResNet34模型测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试摄动法集成...")
    
    # 测试模型兼容性
    model_test = test_model_compatibility()
    
    # 测试摄动法集成
    integration_test = test_perturbation_integration()
    
    if model_test and integration_test:
        print("\n🎉 所有测试通过！摄动法已成功集成到您的训练代码中。")
        print("\n使用说明：")
        print("1. 运行 python train_0211.py 开始训练")
        print("2. 训练将分为两个阶段：")
        print("   - 第一阶段：常规ResNet训练")
        print("   - 第二阶段：摄动法优化")
        print("3. 可以通过修改train_0211.py中的摄动法配置参数来调整优化效果")
        print("4. 训练结果将保存可视化图表，包含两个阶段的对比")
    else:
        print("\n❌ 测试失败，请检查代码集成")
