import os
import sys
import json
import time
import copy

import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms, datasets
from tqdm import tqdm
from torch.utils.data import DataLoader, Dataset
from model import resnet34, resnet18
from perturbation_optimizer import PerturbationOptimizer


class TransformerEncoder(nn.Module):
    def __init__(self, dim):
        super(TransformerEncoder, self).__init__()
        self.weights = nn.Parameter(torch.randn(64, 64))  # 初始化权重

    def forward(self, patches):
        patches_flattened = patches.view(patches.size(0), -1)
        patches_new = patches_flattened.clone()
        for i in range(patches_new.size(0)):
            for j in range(i + 1, patches_new.size(0)):
                patches_new[i] = self.orthogonalize_i(patches_new.clone(), i, j)
                patches_new[j] = self.orthogonalize_j(patches_new.clone(), i, j)
        return patches_new

    def orthogonalize_j(self, patches_flattened, i, j):
        normalized_i = patches_flattened[i] / torch.norm(patches_flattened[i])
        projection_length = torch.matmul(patches_flattened[j].view(1, -1), normalized_i.view(-1, 1)).squeeze()
        projection_vector = projection_length * normalized_i
        orthogonal_j = patches_flattened[j].clone() - projection_vector
        return orthogonal_j

    def orthogonalize_i(self, patches_flattened, i, j):
        normalized_j = patches_flattened[j] / torch.norm(patches_flattened[j])
        projection_length = torch.matmul(patches_flattened[i].view(1, -1), normalized_j.view(-1, 1)).squeeze()
        projection_vector = projection_length * normalized_j
        orthogonal_i = patches_flattened[i].clone() - projection_vector
        return orthogonal_i

class CustomImageDataset(Dataset):
    def __init__(self, image_folder, transform=None):
        self.dataset = datasets.ImageFolder(image_folder)
        self.transform = transform
        self.encoder = TransformerEncoder(dim=768)  # 设置维度为768

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        image, label = self.dataset[idx]
        if self.transform:
            image = self.transform(image)
        # 展平图片进行 TransformerEncoder 的处理
        image = image.view(1, -1)  # 将图片展平
        image = self.encoder(image)  # 进行正交化处理
        image = image.view(3, 224, 224)  # 重新调整维度
        return image, label


def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print("using {} device.".format(device))

    # ===== 训练配置参数 =====
    data_transform = {
        "train": transforms.Compose([transforms.RandomResizedCrop(224),
                                     transforms.RandomHorizontalFlip(),
                                     transforms.ToTensor(),
                                     transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])]),
        "val": transforms.Compose([transforms.Resize(256),
                                   transforms.CenterCrop(224),
                                   transforms.ToTensor(),
                                   transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])}
    image_path = '/media/lwq/dd1/code/Test5_resnet/花卉五类分类数据集'  # 数据地址
    # 石头七类 数据集地址： /media/lwq/dd1/data/石头分类数据集/石头-七类-分类数据集
    # 河道污染 4 类数据集地址：/media/lwq/dd1/code/Test5_resnet/河道垃圾数据集/河道污染等级训练数据集
    # 花 数据集 5类 地址：/media/lwq/dd1/code/Test5_resnet/花卉五类分类数据集
    epochs = 60
    batch_size = 32

    # ===== 摄动法配置参数 =====
    enable_perturbation = True  # 是否启用摄动法优化
    perturbation_iterations = 5  # 摄动法迭代次数
    perturbation_strength = 0.01  # 摄动强度
    top_k = 10  # 每次选择的最大误差样本数

    # data_root = os.path.abspath(os.path.join(os.getcwd(), "../.."))  # get data root path
    # image_path = os.path.join(data_root, "data_set", "flower_data")  # flower data set path
    assert os.path.exists(image_path), "{} path does not exist.".format(image_path)
    # train_dataset = datasets.ImageFolder(root=os.path.join(image_path, "train"),
    #                                      transform=data_transform["train"])
    train_dataset = CustomImageDataset(image_folder=os.path.join(image_path, "train"),
                                       transform=data_transform["train"])
    train_num = len(train_dataset)

    # {'daisy':0, 'dandelion':1, 'roses':2, 'sunflower':3, 'tulips':4}
    # {'ponding':0, 'shadow':1}  0 为积水  ， 1 为阴影
    # flower_list = train_dataset.class_to_idx
    # cla_dict = dict((val, key) for key, val in flower_list.items())
    # # write dict into json file
    # json_str = json.dumps(cla_dict, indent=4)  # indent 用于空格数 4位
    # with open('class_indices.json', 'w') as json_file:
    #     json_file.write(json_str)

    nw = min([os.cpu_count(), batch_size if batch_size > 1 else 0, 8])  # number of workers
    print('Using {} dataloader workers every process'.format(nw))

    train_loader = torch.utils.data.DataLoader(train_dataset,
                                               batch_size=batch_size, shuffle=True,
                                               num_workers=nw)
    train_steps = len(train_loader)
    validate_dataset = CustomImageDataset(image_folder=os.path.join(image_path, "val"),
                                       transform=data_transform["val"])
    val_num = len(validate_dataset)
    validate_loader = torch.utils.data.DataLoader(validate_dataset,
                                                  batch_size=batch_size, shuffle=False,
                                                  num_workers=nw)

    print("using {} images for training, {} images for validation.".format(train_num,
                                                                           val_num))

    net = resnet34()
    # net = resnet18()
    # load pretrain weights
    # download url: https://download.pytorch.org/models/resnet34-333f7ec4.pth

    # 使用预训练权重
    # model_weight_path = "./pre_weight/resnet34-pre.pth"
    # assert os.path.exists(model_weight_path), "file {} does not exist.".format(model_weight_path)
    # net.load_state_dict(torch.load(model_weight_path, map_location='cpu'))

    # 不使用预训练权重

    # for param in net.parameters():
    #     param.requires_grad = False

    # change fc layer structure
    in_channel = net.fc.in_features
    net.fc = nn.Linear(in_channel, 5)  # 石头分类数据集7类， 花分类数据分类5类  积水数据分为2类 河道污染等级分4类
    net.to(device)

    # define loss function
    loss_function = nn.CrossEntropyLoss()

    # construct an optimizer
    params = [p for p in net.parameters() if p.requires_grad]
    # optimizer = optim.Adam(params, lr=0.001)
    # 在训练过程中，使用如下方式添加L2正则化：
    optimizer = optim.SGD(params, lr=0.01, momentum=0.9, weight_decay=1e-4)

    best_acc = 0.0
    save_path = './weight/best_wuran_resNet34.pth'
    train_steps = len(train_loader)
    epochs_list = []
    train_loss_list = []
    val_acc_list = []

    for epoch in range(epochs):
        # train
        epochs_list.append(epoch)
        net.train()
        running_loss = 0.0
        train_bar = tqdm(train_loader, file=sys.stdout)
        for step, data in enumerate(train_bar):
            images, labels = data
            optimizer.zero_grad()
            logits = net(images.to(device))
            loss = loss_function(logits, labels.to(device))
            loss.backward()
            optimizer.step()

            # print statistics
            running_loss += loss.item()

            train_bar.desc = "train epoch[{}/{}] loss:{:.3f}".format(epoch + 1,
                                                                     epochs,
                                                                     loss)

        train_loss_list.append(round(running_loss / train_steps, 3))
        # validate
        net.eval()
        acc = 0.0  # accumulate accurate number / epoch
        with torch.no_grad():
            val_bar = tqdm(validate_loader, file=sys.stdout)
            for val_data in val_bar:
                val_images, val_labels = val_data
                outputs = net(val_images.to(device))
                # loss = loss_function(outputs, test_labels)
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels.to(device)).sum().item()

                val_bar.desc = "valid epoch[{}/{}]".format(epoch + 1,
                                                           epochs)

        val_accurate = acc / val_num
        val_acc_list.append(round(val_accurate, 3))
        print('[epoch %d] train_loss: %.3f  val_accuracy: %.3f' %
              (epoch + 1, running_loss / train_steps, val_accurate))

        if val_accurate > best_acc:
            best_acc = val_accurate
            torch.save(net.state_dict(), save_path)

    print('第一阶段常规训练完成，最佳准确率: {:.4f}'.format(best_acc))

    # ===== 第二阶段：摄动法优化 =====
    if enable_perturbation:
        print("\n=== 开始第二阶段：摄动法优化 ===")
        print(f"摄动参数配置:")
        print(f"  - 迭代次数: {perturbation_iterations}")
        print(f"  - 摄动强度: {perturbation_strength}")
        print(f"  - 样本选择数: {top_k}")

        # 加载最佳模型进行摄动优化
        net.load_state_dict(torch.load(save_path, map_location=device))

        # 创建摄动法优化器
        perturbation_optimizer = PerturbationOptimizer(
            model=net,
            device=device,
            perturbation_strength=perturbation_strength
        )

        # 执行摄动法优化
        optimization_history = perturbation_optimizer.perturbation_optimize(
            dataloader=train_loader,
            num_iterations=perturbation_iterations,
            top_k=top_k
        )

        # 评估摄动法优化效果
        improvement_metrics = perturbation_optimizer.evaluate_improvement(validate_loader)

        print("\n=== 摄动法优化结果 ===")
        print(f"原始准确率: {improvement_metrics['original_accuracy']:.4f}")
        print(f"优化后准确率: {improvement_metrics['optimized_accuracy']:.4f}")
        print(f"准确率提升: {improvement_metrics['accuracy_improvement']:.4f} ({improvement_metrics['accuracy_improvement']/improvement_metrics['original_accuracy']*100:+.2f}%)")
        print(f"原始损失: {improvement_metrics['original_loss']:.4f}")
        print(f"优化后损失: {improvement_metrics['optimized_loss']:.4f}")
        print(f"损失降低: {improvement_metrics['loss_reduction']:.4f} ({improvement_metrics['loss_reduction']/improvement_metrics['original_loss']*100:+.2f}%)")

        # 保存摄动优化后的模型
        optimized_save_path = save_path.replace('.pth', '_perturbation_optimized.pth')
        torch.save(net.state_dict(), optimized_save_path)
        print(f"摄动优化后的模型已保存到: {optimized_save_path}")

        # 更新最佳准确率用于最终显示
        final_best_acc = improvement_metrics['optimized_accuracy']
    else:
        optimization_history = None
        improvement_metrics = None
        final_best_acc = best_acc

    print('所有训练阶段完成！')

    # ===== 绘制训练结果 =====
    import matplotlib.pyplot as plt

    if enable_perturbation and optimization_history is not None:
        # 绘制包含摄动法优化的完整结果
        plot_complete_training_results(epochs_list, train_loss_list, val_acc_list,
                                     optimization_history, improvement_metrics,
                                     best_acc, final_best_acc)
    else:
        # 绘制常规训练结果
        plot_basic_training_results(epochs_list, train_loss_list, val_acc_list)


def plot_basic_training_results(epochs_list, train_loss_list, val_acc_list):
    """绘制基础训练结果"""
    import matplotlib.pyplot as plt
    plt.figure(figsize=(10, 5))

    plt.plot(epochs_list, train_loss_list, label='train loss', marker='o')
    plt.plot(epochs_list, val_acc_list, label='val acc', marker='s')

    # 添加标题和标签
    plt.title('Training Loss and Validation Accuracy')
    plt.xlabel('Epochs')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('./test_image/0620_hua_basic.png', dpi=300, bbox_inches='tight')
    plt.show()


def plot_complete_training_results(epochs_list, train_loss_list, val_acc_list,
                                 optimization_history, improvement_metrics,
                                 base_best_acc, final_best_acc):
    """绘制包含摄动法优化的完整训练结果"""
    import matplotlib.pyplot as plt
    plt.figure(figsize=(15, 5))

    # 子图1：常规训练过程
    plt.subplot(1, 3, 1)
    plt.plot(epochs_list, train_loss_list, label='Train Loss', marker='o', linewidth=2)
    plt.plot(epochs_list, val_acc_list, label='Val Accuracy', marker='s', linewidth=2)
    plt.title(f'Stage 1: Base Training\n(Best Acc: {base_best_acc:.4f})')
    plt.xlabel('Epochs')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图2：摄动法优化过程
    plt.subplot(1, 3, 2)
    iterations = [h['iteration'] for h in optimization_history]
    avg_errors = [h['avg_error'] for h in optimization_history]
    max_errors = [h['max_error'] for h in optimization_history]

    plt.plot(iterations, avg_errors, label='Average Error', marker='o', linewidth=2)
    plt.plot(iterations, max_errors, label='Max Error', marker='s', linewidth=2)
    plt.title('Stage 2: Perturbation Optimization')
    plt.xlabel('Iteration')
    plt.ylabel('Error')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图3：优化效果对比
    plt.subplot(1, 3, 3)
    categories = ['Accuracy', 'Loss']
    original_values = [improvement_metrics['original_accuracy'], improvement_metrics['original_loss']]
    optimized_values = [improvement_metrics['optimized_accuracy'], improvement_metrics['optimized_loss']]

    x = range(len(categories))
    width = 0.35

    plt.bar([i - width/2 for i in x], original_values, width, label='After Stage 1', alpha=0.7)
    plt.bar([i + width/2 for i in x], optimized_values, width, label='After Stage 2', alpha=0.7)

    plt.title(f'Two-Stage Training Results\n(Final Acc: {final_best_acc:.4f})')
    plt.xlabel('Metrics')
    plt.ylabel('Value')
    plt.xticks(x, categories)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 添加改进数值标注
    for i, (orig, opt) in enumerate(zip(original_values, optimized_values)):
        improvement = opt - orig
        plt.text(i, max(orig, opt) + 0.05, f'{improvement:+.4f}',
                ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('./test_image/0620_hua_with_perturbation.png', dpi=300, bbox_inches='tight')
    plt.show()


if __name__ == '__main__':
    main()
