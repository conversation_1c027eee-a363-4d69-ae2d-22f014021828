#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄动法优化器 - 基于您文档中的摄动法理论实现
用于在常规训练后进一步优化网络性能
"""

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
import copy
from tqdm import tqdm

class PerturbationOptimizer:
    """
    摄动法优化器
    基于文档中的摄动法理论，对已训练的网络进行后优化
    """
    
    def __init__(self, model, device='cuda', perturbation_strength=0.01):
        """
        初始化摄动法优化器
        
        Args:
            model: 已训练的模型
            device: 计算设备
            perturbation_strength: 摄动强度δ
        """
        self.model = model
        self.device = device
        self.perturbation_strength = perturbation_strength
        self.original_model = copy.deepcopy(model)
        
    def calculate_sample_errors(self, dataloader, criterion):
        """
        计算每个样本的输出误差
        对应文档中的误差计算步骤
        
        Args:
            dataloader: 数据加载器
            criterion: 损失函数
            
        Returns:
            errors: 每个样本的误差
            samples: 样本数据
            labels: 样本标签
            predictions: 模型预测
        """
        self.model.eval()
        errors = []
        all_samples = []
        all_labels = []
        all_predictions = []
        
        with torch.no_grad():
            for batch_idx, (data, target) in enumerate(dataloader):
                data, target = data.to(self.device), target.to(self.device)
                
                # 获取模型输出
                output = self.model(data)
                
                # 计算每个样本的误差
                for i in range(data.size(0)):
                    sample_output = output[i:i+1]
                    sample_target = target[i:i+1]
                    
                    # 计算单个样本的损失作为误差
                    error = criterion(sample_output, sample_target).item()
                    errors.append(error)
                    all_samples.append(data[i])
                    all_labels.append(target[i])
                    all_predictions.append(sample_output.squeeze())
        
        return errors, all_samples, all_labels, all_predictions
    
    def find_max_error_samples(self, errors, samples, labels, predictions, top_k=10):
        """
        找出误差最大的样本
        对应文档中"挑选最大者"的步骤
        
        Args:
            errors: 样本误差列表
            samples: 样本数据
            labels: 样本标签
            predictions: 模型预测
            top_k: 选择前k个最大误差样本
            
        Returns:
            max_error_indices: 最大误差样本的索引
        """
        # 找出误差最大的样本索引
        error_indices = np.argsort(errors)[-top_k:]  # 取最大的top_k个
        return error_indices
    
    def apply_label_perturbation(self, labels, max_error_indices, num_classes):
        """
        对最大误差样本的标签进行摄动
        对应文档中的标签摄动步骤
        
        Args:
            labels: 原始标签
            max_error_indices: 最大误差样本索引
            num_classes: 类别数量
            
        Returns:
            perturbed_labels: 摄动后的标签
        """
        perturbed_labels = labels.clone()
        
        for idx in max_error_indices:
            original_label = labels[idx].item()
            
            # 根据文档中的方法确定摄动方向
            # 如果是正类(标签0)，摄动为负；如果是负类，摄动为正
            if original_label == 0:
                # 正类，添加负摄动
                perturbation = -self.perturbation_strength
            else:
                # 负类或其他类，添加正摄动
                perturbation = self.perturbation_strength
            
            # 创建软标签（用于支持连续值标签）
            # 这里我们使用one-hot编码的摄动版本
            soft_label = torch.zeros(num_classes, device=self.device)
            soft_label[original_label] = 1.0 + perturbation
            
            # 确保标签值在合理范围内
            soft_label = torch.clamp(soft_label, 0.0, 1.0)
            soft_label = soft_label / soft_label.sum()  # 归一化
            
            # 将软标签转换回硬标签（选择概率最大的类别）
            perturbed_labels[idx] = soft_label.argmax()
        
        return perturbed_labels
    
    def least_squares_update(self, samples, perturbed_labels, learning_rate=0.001):
        """
        使用最小二乘法更新网络参数
        对应文档中的最小二乘解计算
        
        Args:
            samples: 样本数据
            perturbed_labels: 摄动后的标签
            learning_rate: 学习率
        """
        self.model.train()
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.SGD(self.model.parameters(), lr=learning_rate)
        
        # 创建小批量数据进行更新
        batch_size = min(32, len(samples))
        for i in range(0, len(samples), batch_size):
            end_idx = min(i + batch_size, len(samples))
            batch_samples = torch.stack(samples[i:end_idx]).to(self.device)
            batch_labels = perturbed_labels[i:end_idx].to(self.device)
            
            optimizer.zero_grad()
            outputs = self.model(batch_samples)
            loss = criterion(outputs, batch_labels)
            loss.backward()
            optimizer.step()
    
    def perturbation_optimize(self, dataloader, num_iterations=5, top_k=10):
        """
        执行摄动法优化
        对应文档中的完整摄动法算法
        
        Args:
            dataloader: 训练数据加载器
            num_iterations: 迭代次数
            top_k: 每次选择的最大误差样本数量
            
        Returns:
            optimization_history: 优化历史记录
        """
        criterion = nn.CrossEntropyLoss()
        optimization_history = []
        
        print("开始摄动法优化...")
        
        for iteration in range(num_iterations):
            print(f"\n=== 摄动法优化 第{iteration+1}轮 ===")
            
            # 步骤1: 计算样本误差
            print("1. 计算样本误差...")
            errors, samples, labels, predictions = self.calculate_sample_errors(dataloader, criterion)
            avg_error = np.mean(errors)
            max_error = np.max(errors)
            
            print(f"   平均误差: {avg_error:.4f}, 最大误差: {max_error:.4f}")
            
            # 步骤2: 找出最大误差样本
            print("2. 识别最大误差样本...")
            max_error_indices = self.find_max_error_samples(
                errors, samples, labels, predictions, top_k=top_k
            )
            print(f"   选择了 {len(max_error_indices)} 个最大误差样本")
            
            # 步骤3: 应用标签摄动
            print("3. 应用标签摄动...")
            num_classes = len(torch.unique(torch.stack(labels)))
            perturbed_labels = self.apply_label_perturbation(
                torch.stack(labels), max_error_indices, num_classes
            )
            
            # 步骤4: 最小二乘更新
            print("4. 执行最小二乘更新...")
            self.least_squares_update(samples, perturbed_labels)
            
            # 记录优化历史
            optimization_history.append({
                'iteration': iteration + 1,
                'avg_error': avg_error,
                'max_error': max_error,
                'num_perturbed_samples': len(max_error_indices)
            })
            
            print(f"   第{iteration+1}轮优化完成")
        
        print("\n摄动法优化完成！")
        return optimization_history
    
    def evaluate_improvement(self, test_dataloader):
        """
        评估摄动法优化的效果
        
        Args:
            test_dataloader: 测试数据加载器
            
        Returns:
            improvement_metrics: 改进指标
        """
        criterion = nn.CrossEntropyLoss()
        
        # 评估原始模型
        original_acc, original_loss = self._evaluate_model(self.original_model, test_dataloader, criterion)
        
        # 评估优化后模型
        optimized_acc, optimized_loss = self._evaluate_model(self.model, test_dataloader, criterion)
        
        improvement_metrics = {
            'original_accuracy': original_acc,
            'optimized_accuracy': optimized_acc,
            'accuracy_improvement': optimized_acc - original_acc,
            'original_loss': original_loss,
            'optimized_loss': optimized_loss,
            'loss_reduction': original_loss - optimized_loss
        }
        
        return improvement_metrics
    
    def _evaluate_model(self, model, dataloader, criterion):
        """
        评估模型性能
        """
        model.eval()
        total_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in dataloader:
                data, target = data.to(self.device), target.to(self.device)
                output = model(data)
                loss = criterion(output, target)
                total_loss += loss.item()
                
                pred = output.argmax(dim=1)
                correct += pred.eq(target).sum().item()
                total += target.size(0)
        
        accuracy = correct / total
        avg_loss = total_loss / len(dataloader)
        
        return accuracy, avg_loss
