import os
import json

import torch
from PIL import Image
from torchvision import transforms
import matplotlib.pyplot as plt

from model import resnet34, resnet18


def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    data_transform = transforms.Compose(
        [transforms.Resize(256),
         transforms.CenterCrop(224),
         transforms.ToTensor(),
         transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])

    # 模型加载
    # create model
    model = resnet34(num_classes=4).to(device)
    # load model weights
    model_weight_path = "./weight/best_wuran_resNet34.pth"
    model.load_state_dict(torch.load(model_weight_path, map_location=device))
    model.eval()

    # read class_indict
    json_path = './class_indices_1.json'
    assert os.path.exists(json_path), "file: '{}' dose not exist.".format(json_path)

    with open(json_path, "r") as f:
        class_indict = json.load(f)

    img_file = './测试文件夹'
    img_list = os.listdir(img_file)

    # 渲染
    # 确定每行显示的图片数
    cols = 3  # 每行两列图片
    rows = len(img_list) // cols  # 计算行数，向下取整
    if len(img_list) % cols > 0:
        rows += 1  # 如果有剩余的图片，增加一行

    # 创建一个figure和axes对象
    fig, axs = plt.subplots(nrows=rows, ncols=cols, figsize=(10, rows * 5))  # 调整figsize以适应你的需要

    for i, images in enumerate(img_list):
        if images.endswith('.jpg'):

            image_name = images.split('.jpg')[0]

            row_idx = i // cols
            col_idx = i % cols

            img_path = os.path.join(img_file, images)
            img = Image.open(img_path)
            # 在对应的axes上显示图片
            axs[row_idx, col_idx].imshow(img)
            axs[row_idx, col_idx].axis('on')  # 关闭坐标轴
            # [N, C, H, W]
            img = data_transform(img)
            # expand batch dimension
            img = torch.unsqueeze(img, dim=0)
            with torch.no_grad():
                # predict class
                output = torch.squeeze(model(img.to(device))).cpu()
                predict = torch.softmax(output, dim=0)
                predict_cla = torch.argmax(predict).numpy()

            print_res = "name: {} class: {}   prob: {:.3}".format(image_name, class_indict[str(predict_cla)],
                                                         predict[predict_cla].numpy())
            print(img_path)
            print(print_res)
            # 如果axes存在（不是最后一行多余的空位置），则添加描述
            if axs[row_idx, col_idx].get_xaxis().get_visible():
                axs[row_idx, col_idx].set_title(print_res, fontsize=8)  # 调整字体大小以适应图片大小

            # plt.title(print_res)
            # plt.show()
    # 调整子图间距以适应标题
    plt.tight_layout()

    # 显示整个figure
    plt.savefig('result-2.jpg')
    plt.show()


if __name__ == '__main__':
    main()
